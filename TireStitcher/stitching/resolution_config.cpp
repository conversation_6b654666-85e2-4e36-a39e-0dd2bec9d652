#include "config.h"

// Implementation of resolution-specific parameter methods

void StitchConfig::applyResolutionParams() {
    if (resolutionMode == "4K") {
        currentParams = get4KParams();
        safePrint("Applied 4K resolution parameters", verbose, *this);
    } else {
        currentParams = get8KParams();
        safePrint("Applied 8K resolution parameters", verbose, *this);
    }

    // Override individual parameters if explicitly set
    if (templateMatchPrecision != 2) { // 2 is the default, so if different, user set it
        currentParams.templateMatchPrecision = templateMatchPrecision;
        safePrint("Overriding template match precision with user value: " +
                 std::to_string(templateMatchPrecision), verbose, *this);
    } else {
        // Update the main config with resolution-specific value
        templateMatchPrecision = currentParams.templateMatchPrecision;
    }

    if (jpegQuality != 98) { // 98 is the default, so if different, user set it
        currentParams.jpegQuality = jpegQuality;
        safePrint("Overriding JPEG quality with user value: " +
                 std::to_string(jpegQuality), verbose, *this);
    } else {
        // Update the main config with resolution-specific value
        jpegQuality = currentParams.jpegQuality;
    }
}

ResolutionParams StitchConfig::get4KParams() {
    ResolutionParams params;

    // Strip width and overlap parameters optimized for 4K (3840x2160)
    params.stripWidthMargin = 0.15;        // 15% margin for strip width
    params.overlapPercentage = 0.10;       // 10% of movement as overlap
    params.fallbackStripWidth = 20;        // Smaller fallback for 4K
    params.minOverlapPixels = 5;           // Minimum overlap for 4K images

    // Template matching parameters - medium precision for speed
    params.templateMatchPrecision = 1;     // Medium precision (faster)
    params.correlationThreshold = 0.75;    // Slightly lower threshold for 4K
    params.highCorrelationThreshold = 0.90; // Lower high correlation threshold

    // Movement detection parameters
    params.opticalFlowScale = 0.3;         // Larger scale for 4K (less downscaling)
    params.smallMovementThreshold = 3.0;   // Lower threshold for 4K
    params.defaultMovementBase = 50.0;     // Smaller base movement for 4K
    params.defaultMovementScale = 8.0;     // Smaller scale factor

    // Performance parameters optimized for 4K
    params.framesPerTile = 150;            // More frames per tile for 4K
    params.movingAverageSize = 3;          // Smaller moving average for responsiveness
    params.preloadBatchSize = 25;          // More preloading for 4K

    // Quality parameters
    params.jpegQuality = 95;               // Slightly lower quality for 4K
    params.useEnhancedBlending = false;    // Simpler blending for speed

    return params;
}

ResolutionParams StitchConfig::get8KParams() {
    ResolutionParams params;

    // Strip width and overlap parameters - RESTORED ORIGINAL WORKING VALUES
    params.stripWidthMargin = 0.10;        // 10% margin for strip width (ORIGINAL)
    params.overlapPercentage = 0.10;       // 10% of movement as overlap (ORIGINAL)
    params.fallbackStripWidth = 50;        // Larger fallback for 8K (ORIGINAL)
    params.minOverlapPixels = 10;          // REDUCED: Was 20, but this may cause width compression

    // Template matching parameters - RESTORED ORIGINAL WORKING VALUES
    params.templateMatchPrecision = 2;     // High precision (ORIGINAL)
    params.correlationThreshold = 0.75;    // RESTORED: Original working value was 0.75, not 0.80
    params.highCorrelationThreshold = 0.92; // RESTORED: Original working value was 0.92, not 0.95

    // Movement detection parameters - ORIGINAL WORKING VALUES
    params.opticalFlowScale = 0.25;        // Smaller scale for 8K (ORIGINAL)
    params.smallMovementThreshold = 5.0;   // Higher threshold for 8K (ORIGINAL)
    params.defaultMovementBase = 75.0;     // Larger base movement for 8K (ORIGINAL)
    params.defaultMovementScale = 10.0;    // Larger scale factor (ORIGINAL)

    // Performance parameters - ORIGINAL WORKING VALUES
    params.framesPerTile = 100;            // Standard frames per tile for 8K (ORIGINAL)
    params.movingAverageSize = 5;          // Standard moving average size (ORIGINAL)
    params.preloadBatchSize = 20;          // Standard preloading for 8K (ORIGINAL)

    // Quality parameters - ORIGINAL WORKING VALUES
    params.jpegQuality = 98;               // High quality for 8K (ORIGINAL)
    params.useEnhancedBlending = true;     // Enhanced blending for quality (ORIGINAL)

    return params;
}
